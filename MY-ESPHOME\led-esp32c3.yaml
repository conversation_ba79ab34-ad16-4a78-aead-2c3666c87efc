esphome:
  name: voc-myesp32c3
  # name: voc-esp32c3
  friendly_name: "VOC-CO2-HCHO ESP32-C3 Sensor"

esp32:
  board: airm2m_core_esp32c3
  framework:
    type: arduino


# 二进制传感器
binary_sensor:
  # 设备状态
  - platform: status
    name: "Status"

# 开关控制
switch:
  # 重启开关
  - platform: restart
    name: "Restart"

  # LED灯带开关
  - platform: template
    name: "LED Strip Power"
    id: led_power
    optimistic: true
    turn_on_action:
      - light.turn_on: led_strip
    turn_off_action:
      - light.turn_off: led_strip

# 日志配置
logger:
  baud_rate: 115200
  level: INFO  # 显示传感器数据
  logs:
    voc_uart: INFO    # 显示VOC传感器数据
    uart_debug: WARN  # 减少UART调试信息
    uart: WARN        # 减少UART日志
    wifi: WARN        # 减少WiFi日志
    api: WARN         # 减少API日志
    ota: WARN         # 减少OTA日志
    web_server: WARN  # 减少Web服务器日志

# OTA更新
ota:
  platform: esphome

# API配置
api:

# Web服务器
web_server:
  port: 80
  auth:
    username: admin
    password: !secret wifi_password

# WiFi配置
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

  # 启用回退热点（可选）
  ap:
    ssid: "ESP32-C3 Fallback Hotspot"
    password: "12345678"

captive_portal:

# 传感器
sensor:
  # WiFi信号强度
  - platform: wifi_signal
    name: "WiFi Signal"
    update_interval: 60s

  # 运行时间
  - platform: uptime
    name: "Uptime"
    update_interval: 60s

# 文本传感器
text_sensor:
  # WiFi信息
  - platform: wifi_info
    ip_address:
      name: "IP Address"
    ssid:
      name: "Connected SSID"
    mac_address:
      name: "Mac Address"

# WS2812 LED灯带配置
light:
  - platform: neopixel
    type: GRB
    variant: WS2812
    pin: GPIO8  # 可以根据你的接线修改引脚
    num_leds: 30  # LED数量，根据你的灯带修改
    name: "WS2812 LED Strip"
    id: led_strip
    effects:
      - addressable_rainbow:
          name: Rainbow
          speed: 10
          width: 50
      - addressable_color_wipe:
          name: Color Wipe
          colors:
            - red: 100%
              green: 100%
              blue: 100%
              num_leds: 1
            - red: 0%
              green: 0%
              blue: 0%
              num_leds: 1
          add_led_interval: 100ms
          reverse: false
      - addressable_scan:
          name: Scan
          move_interval: 100ms
          scan_width: 1
      - addressable_twinkle:
          name: Twinkle
          twinkle_probability: 5%
          progress_interval: 4ms
      - addressable_random_twinkle:
          name: Random Twinkle
          twinkle_probability: 5%
          progress_interval: 32ms
      - addressable_fireworks:
          name: Fireworks
          update_interval: 32ms
          spark_probability: 10%
          use_random_color: false
          fade_out_rate: 120
      - addressable_flicker:
          name: Flicker
          update_interval: 16ms
          intensity: 5%
