esphome:
  name: led-myesp32c3
  friendly_name: "VOC-CO2-HCHO ESP32-C3 Sensor"

esp32:
  board: airm2m_core_esp32c3
  framework:
    type: arduino


# 二进制传感器
binary_sensor:
  # 设备状态
  - platform: status
    name: "Status"

# 开关控制
switch:
  # 重启开关
  - platform: restart
    name: "Restart"

  # LED灯带开关
  - platform: template
    name: "LED Strip Power"
    id: led_power
    optimistic: true
    turn_on_action:
      - light.turn_on: led_strip
    turn_off_action:
      - light.turn_off: led_strip

  # 自动检测LED数量
  - platform: template
    name: "Auto Detect LED Count"
    id: auto_detect_leds
    turn_on_action:
      - lambda: |-
          // 自动检测LED数量的逻辑
          ESP_LOGI("led_detect", "开始自动检测LED数量...");

          // 先关闭LED
          auto call = id(led_strip).make_call();
          call.set_state(false);
          call.perform();

          // 测试不同的LED数量 (从1到300)
          int detected_count = 30; // 默认值

          // 这里可以实现更复杂的检测逻辑
          // 目前设置为用户手动输入的值
          detected_count = (int)id(led_count).state;

          ESP_LOGI("led_detect", "检测到LED数量: %d", detected_count);

          // 更新LED数量
          id(led_count).publish_state(detected_count);
    turn_off_action:
      - lambda: |-
          ESP_LOGI("led_detect", "停止LED检测");

# 日志配置
logger:
  baud_rate: 115200
  level: INFO  # 显示传感器数据
  logs:
    voc_uart: INFO    # 显示VOC传感器数据
    uart_debug: WARN  # 减少UART调试信息
    uart: WARN        # 减少UART日志
    wifi: WARN        # 减少WiFi日志
    api: WARN         # 减少API日志
    ota: WARN         # 减少OTA日志
    web_server: WARN  # 减少Web服务器日志

# OTA更新
ota:
  platform: esphome

# API配置
api:

# Web服务器
web_server:
  port: 80
  auth:
    username: admin
    password: !secret wifi_password

# WiFi配置
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

  # 启用回退热点（可选）
  ap:
    ssid: "ESP32-C3 Fallback Hotspot"
    password: "12345678"

captive_portal:

# 脚本 - LED扫描检测
script:
  - id: led_scan_detect
    mode: restart
    then:
      - lambda: |-
          ESP_LOGI("led_scan", "开始LED扫描检测...");

          // 设置最大检测数量
          int max_test = 300;
          int current_num = (int)id(led_count).state;

          // 先关闭所有LED
          auto call = id(led_strip).make_call();
          call.set_state(false);
          call.perform();

          delay(500);

          // 逐个点亮LED进行测试
          for(int i = 1; i <= max_test; i++) {
            // 设置当前LED数量
            id(led_strip).set_num_leds(i);

            // 点亮最后一个LED (红色)
            auto call = id(led_strip).make_call();
            call.set_state(true);
            call.set_brightness(0.5);
            call.set_rgb(1.0, 0.0, 0.0); // 红色

            // 只点亮最后一个LED
            std::vector<Color> colors(i, Color::BLACK);
            colors[i-1] = Color(255, 0, 0); // 最后一个LED红色

            call.set_effect("None");
            call.perform();

            delay(100); // 等待100ms观察

            // 这里可以添加用户确认逻辑
            // 实际应用中可能需要用户手动停止或确认
          }

          ESP_LOGI("led_scan", "LED扫描完成");

  # LED计数确认脚本
  - id: confirm_led_count
    parameters:
      count: int
    then:
      - lambda: |-
          ESP_LOGI("led_confirm", "确认LED数量: %d", count);
          id(led_count).publish_state(count);

          // 重新配置LED灯带
          id(led_strip).set_num_leds(count);

          // 显示确认效果 - 全部闪烁绿色3次
          for(int i = 0; i < 3; i++) {
            auto call = id(led_strip).make_call();
            call.set_state(true);
            call.set_rgb(0.0, 1.0, 0.0); // 绿色
            call.set_brightness(0.8);
            call.perform();
            delay(300);

            call.set_state(false);
            call.perform();
            delay(300);
          }

# 传感器
sensor:
  # WiFi信号强度
  - platform: wifi_signal
    name: "WiFi Signal"
    update_interval: 60s

  # 运行时间
  - platform: uptime
    name: "Uptime"
    update_interval: 60s

# 文本传感器
text_sensor:
  # WiFi信息
  - platform: wifi_info
    ip_address:
      name: "IP Address"
    ssid:
      name: "Connected SSID"
    mac_address:
      name: "Mac Address"

# 数字输入 - 动态设置LED数量
number:
  - platform: template
    name: "LED Count"
    id: led_count
    optimistic: true
    min_value: 1
    max_value: 300
    initial_value: 30
    step: 1
    mode: box
    on_value:
      then:
        - lambda: |-
            // 重新配置LED灯带
            auto call = id(led_strip).make_call();
            call.set_state(false);
            call.perform();
            // 延迟后重新初始化
            id(led_strip).set_num_leds((int)x);

# WS2812 LED灯带配置
light:
  - platform: neopixel
    type: GRB
    variant: WS2812
    pin: GPIO8  # 可以根据你的接线修改引脚
    num_leds: 30  # 初始LED数量
    name: "WS2812 LED Strip"
    id: led_strip
    effects:
      - addressable_rainbow:
          name: Rainbow
          speed: 10
          width: 50
      - addressable_color_wipe:
          name: Color Wipe
          colors:
            - red: 100%
              green: 100%
              blue: 100%
              num_leds: 1
            - red: 0%
              green: 0%
              blue: 0%
              num_leds: 1
          add_led_interval: 100ms
          reverse: false
      - addressable_scan:
          name: Scan
          move_interval: 100ms
          scan_width: 1
      - addressable_twinkle:
          name: Twinkle
          twinkle_probability: 5%
          progress_interval: 4ms
      - addressable_random_twinkle:
          name: Random Twinkle
          twinkle_probability: 5%
          progress_interval: 32ms
      - addressable_fireworks:
          name: Fireworks
          update_interval: 32ms
          spark_probability: 10%
          use_random_color: false
          fade_out_rate: 120
      - addressable_flicker:
          name: Flicker
          update_interval: 16ms
          intensity: 5%
